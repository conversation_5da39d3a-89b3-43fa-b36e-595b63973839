import createIntlMiddleware from "next-intl/middleware";
import { NextRequest, NextResponse } from "next/server";
import { routing } from "./i18n";
import { getCurrentUser } from "@/actions/auth-actions";

// Create the internationalization middleware
const handleI18nRouting = createIntlMiddleware(routing);

// Define protected routes (without locale prefix)
const PROTECTED_ROUTES = ["/", "/services", "/profile", "/settings"];

// Define auth routes (without locale prefix)
const AUTH_ROUTES = ["/login", "/register"];

/**
 * Check if a path matches any of the given patterns
 */
function matchesRoutePatterns(pathname: string, patterns: string[]): boolean {
  return patterns.some((pattern) => {
    if (pathname === pattern) return true;
    if (pattern.endsWith("/*")) {
      const basePath = pattern.slice(0, -2);
      return pathname.startsWith(basePath + "/") || pathname === basePath;
    }
    return pathname.startsWith(pattern + "/");
  });
}

/**
 * Extract path without locale prefix
 */
function getPathWithoutLocale(pathname: string): string {
  const segments = pathname.split("/").filter(Boolean);
  const supportedLocales = routing.locales as readonly string[];

  if (segments.length > 0 && supportedLocales.includes(segments[0])) {
    return "/" + segments.slice(1).join("/");
  }

  return pathname;
}

/**
 * Extract locale from pathname
 */
function getLocaleFromPath(pathname: string): string {
  const segments = pathname.split("/").filter(Boolean);
  const supportedLocales = routing.locales as readonly string[];

  if (segments.length > 0 && supportedLocales.includes(segments[0])) {
    return segments[0];
  }

  return routing.defaultLocale;
}

/**
 * Validate authentication using centralized server action
 * Uses the same authentication logic as the rest of the application
 */
async function isUserAuthenticated(): Promise<boolean> {
  try {
    // Use centralized server action for authentication validation
    const result = await getCurrentUser();
    return result.valid;
  } catch (error) {
    console.error("Authentication validation failed in middleware:", error);
    return false;
  }
}

/**
 * Handle protected route authentication
 */
function handleProtectedRoute(
  request: NextRequest,
  pathname: string,
  pathWithoutLocale: string,
  isAuthenticated: boolean,
): NextResponse | null {
  if (!matchesRoutePatterns(pathWithoutLocale, PROTECTED_ROUTES)) {
    return null;
  }

  if (!isAuthenticated) {
    const locale = getLocaleFromPath(pathname);
    const loginUrl = new URL(`/${locale}/login`, request.url);
    loginUrl.searchParams.set("redirect", pathname);
    return NextResponse.redirect(loginUrl);
  }

  return null;
}

/**
 * Handle auth route redirection
 */
function handleAuthRoute(
  request: NextRequest,
  pathname: string,
  pathWithoutLocale: string,
  isAuthenticated: boolean,
): NextResponse | null {
  if (!matchesRoutePatterns(pathWithoutLocale, AUTH_ROUTES)) {
    return null;
  }

  if (isAuthenticated) {
    const locale = getLocaleFromPath(pathname);
    const redirectTo = request.nextUrl.searchParams.get("redirect");
    const redirectUrl =
      redirectTo && redirectTo.startsWith("/")
        ? new URL(redirectTo, request.url)
        : new URL(`/${locale}`, request.url);

    return NextResponse.redirect(redirectUrl);
  }

  return null;
}

/**
 * Check if request should skip middleware
 */
function shouldSkipMiddleware(pathname: string): boolean {
  return (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/api/") ||
    pathname.includes(".") ||
    pathname.startsWith("/favicon")
  );
}

/**
 * Main middleware function
 */
export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files, API routes, and Next.js internals
  if (shouldSkipMiddleware(pathname)) {
    return NextResponse.next();
  }

  // First, let next-intl handle the routing
  const response = handleI18nRouting(request);

  // If next-intl is redirecting, let it handle the redirect
  if (response.status === 302 || response.status === 307) {
    return response;
  }

  // Extract path without locale for route matching
  const pathWithoutLocale = getPathWithoutLocale(pathname);
  const isAuthenticated = await isUserAuthenticated();

  // Handle protected routes
  const protectedRouteResponse = handleProtectedRoute(
    request,
    pathname,
    pathWithoutLocale,
    isAuthenticated,
  );
  if (protectedRouteResponse) {
    return protectedRouteResponse;
  }

  // Handle auth routes
  const authRouteResponse = handleAuthRoute(
    request,
    pathname,
    pathWithoutLocale,
    isAuthenticated,
  );
  if (authRouteResponse) {
    return authRouteResponse;
  }

  // Return the response from next-intl middleware
  return response;
}

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)
  matcher: "/((?!api|trpc|_next|_vercel|.*\\..*).*)",
};
