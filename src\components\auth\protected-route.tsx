"use client";

import React, { ReactNode, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStatus } from "@/hooks/auth-context";
import { AuthErrorBoundary } from "./auth-error-boundary";
import { useAuth } from "@/providers";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Protected route props interface
 * Following clean code principles with clear prop definitions
 */
interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
}

/**
 * Route guard props interface
 * For more granular control over route protection
 */
interface RouteGuardProps extends ProtectedRouteProps {
  condition: (auth: ReturnType<typeof useAuth>) => boolean;
  redirectCondition?: (auth: ReturnType<typeof useAuth>) => boolean;
}

// ============================================================================
// LOADING COMPONENTS
// ============================================================================

/**
 * Default loading component
 * Following shadcn/ui patterns for consistent styling
 */
function DefaultLoadingComponent() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
}

/**
 * Authentication loading component
 * Specific loading state for authentication checks
 */
function AuthLoadingComponent() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-pulse rounded-full h-8 w-8 bg-primary/20"></div>
        <p className="text-sm text-muted-foreground">
          Checking authentication...
        </p>
      </div>
    </div>
  );
}

// ============================================================================
// ROUTE PROTECTION COMPONENTS
// ============================================================================

/**
 * Protected Route Component
 *
 * Wraps components that require authentication following patterns from:
 * - Clerk: Conditional rendering based on auth status
 * - Supabase: Loading states and error handling
 * - Next.js: Router integration for redirects
 * - Clean Code: Single responsibility and clear interfaces
 */
export function ProtectedRoute({
  children,
  fallback,
  redirectTo = "/login",
  requireAuth = true,
  loadingComponent,
  errorComponent,
}: ProtectedRouteProps) {
  const auth = useAuth();
  const { isSignedIn, isSignedOut, isLoading } = useAuthStatus();
  const router = useRouter();

  // ============================================================================
  // SIDE EFFECTS
  // ============================================================================

  /**
   * Handle authentication-based redirects
   * Following Next.js patterns for programmatic navigation
   */
  useEffect(() => {
    if (!isLoading && requireAuth && isSignedOut) {
      // Construct redirect URL with current path
      const currentPath = window.location.pathname;
      const redirectUrl = new URL(redirectTo, window.location.origin);

      if (currentPath !== redirectTo) {
        redirectUrl.searchParams.set("redirect", currentPath);
      }

      router.push(redirectUrl.toString());
    }
  }, [isLoading, requireAuth, isSignedOut, redirectTo, router]);

  // ============================================================================
  // RENDER LOGIC
  // ============================================================================

  // Show loading state while checking authentication
  if (isLoading) {
    return loadingComponent || <AuthLoadingComponent />;
  }

  // Show fallback or redirect for unauthenticated users
  if (requireAuth && isSignedOut) {
    return fallback || null;
  }

  // Show error component if there's an auth error
  if (auth.error && errorComponent) {
    return errorComponent;
  }

  // Render children for authenticated users
  return { children };
}

/**
 * Signed In Component
 * Following Clerk's pattern for conditional rendering
 * Only renders children when user is authenticated
 */
export function SignedIn({ children }: { children: ReactNode }) {
  const { isSignedIn } = useAuthStatus();

  return isSignedIn ? <>{children}</> : null;
}

/**
 * Signed Out Component
 * Following Clerk's pattern for conditional rendering
 * Only renders children when user is not authenticated
 */
export function SignedOut({ children }: { children: ReactNode }) {
  const { isSignedOut } = useAuthStatus();

  return isSignedOut ? <>{children}</> : null;
}

/**
 * Route Guard Component
 * Advanced route protection with custom conditions
 * Following clean code principles with dependency injection
 */
export function RouteGuard({
  children,
  condition,
  redirectCondition,
  redirectTo = "/login",
  fallback,
  loadingComponent,
}: RouteGuardProps) {
  const auth = useAuth();
  const { isLoading } = useAuthStatus();
  const router = useRouter();

  // ============================================================================
  // SIDE EFFECTS
  // ============================================================================

  /**
   * Handle custom redirect conditions
   */
  useEffect(() => {
    if (!isLoading) {
      const shouldRedirect = redirectCondition
        ? redirectCondition(auth)
        : !condition(auth);

      if (shouldRedirect) {
        router.push(redirectTo);
      }
    }
  }, [isLoading, auth, condition, redirectCondition, redirectTo, router]);

  // ============================================================================
  // RENDER LOGIC
  // ============================================================================

  // Show loading state
  if (isLoading) {
    return loadingComponent || <DefaultLoadingComponent />;
  }

  // Check custom condition
  if (!condition(auth)) {
    return fallback || null;
  }

  // Render children when condition is met
  return <AuthErrorBoundary>{children}</AuthErrorBoundary>;
}

/**
 * Require Authentication HOC
 * Higher-order component for protecting components
 * Following React patterns for component composition
 */
export function requireAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    redirectTo?: string;
    loadingComponent?: ReactNode;
    fallback?: ReactNode;
  },
) {
  const WrappedComponent = (props: P) => {
    return (
      <ProtectedRoute
        redirectTo={options?.redirectTo}
        loadingComponent={options?.loadingComponent}
        fallback={options?.fallback}
      >
        <Component {...props} />
      </ProtectedRoute>
    );
  };

  // Set display name for debugging
  WrappedComponent.displayName = `requireAuth(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Auth Guard Hook
 * Custom hook for imperative auth checking
 * Following React hooks patterns
 */
export function useAuthGuard(options?: {
  redirectTo?: string;
  requireAuth?: boolean;
}) {
  const auth = useAuth();
  const { isSignedIn, isSignedOut, isLoading } = useAuthStatus();
  const router = useRouter();

  const checkAuth = React.useCallback(() => {
    if (!isLoading && options?.requireAuth !== false && isSignedOut) {
      const redirectTo = options?.redirectTo || "/login";
      router.push(redirectTo);
      return false;
    }
    return isSignedIn;
  }, [isLoading, isSignedIn, isSignedOut, router, options]);

  return {
    isAuthenticated: isSignedIn,
    isLoading,
    checkAuth,
    ...auth,
  };
}
