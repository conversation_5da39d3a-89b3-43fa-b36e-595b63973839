import { AsyncServicesTable } from "@/components/features/services-list";
import { Skeleton } from "@/components/ui/skeleton";
import { getTranslations } from "next-intl/server";
import { Suspense } from "react";
import React from "react";
import { ProtectedPage } from "@/components/auth";

function ServicesPageSkeleton() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Skeleton className="h-8 w-[160px]" />
        <Skeleton className="h-4 w-[320px]" />
      </div>
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex space-x-4">
            <Skeleton className="h-4 w-[160px]" />
            <Skeleton className="h-4 w-[240px]" />
            <Skeleton className="h-4 w-[80px]" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        ))}
      </div>
    </div>
  );
}

export default async function ServicesPage() {
  const t = await getTranslations("Services");

  return (
    <ProtectedPage redirectTo="/login">
      <div className="container mx-auto py-10">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t("title")}</h1>
            <p className="text-muted-foreground">{t("subtitle")}</p>
          </div>

          <Suspense fallback={<ServicesPageSkeleton />}>
            <AsyncServicesTable />
          </Suspense>
        </div>
      </div>
    </ProtectedPage>
  );
}
