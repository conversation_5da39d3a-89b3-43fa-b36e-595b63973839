import { DEFAULT_AUTH_CONFIG } from "@/config/env";

/**
 * Clean Token Storage Strategy
 * Uses localStorage for client-side persistence only
 * Simple and focused on single responsibility
 */
export class TokenStorage {
  private static readonly keys = DEFAULT_AUTH_CONFIG.tokenStorageKeys;

  /**
   * Store authentication tokens and user data
   */
  static setTokens(authResponse: AuthResponse): void {
    if (typeof window === "undefined") return;

    try {
      localStorage.setItem(this.keys.accessToken, authResponse.accessToken);
      localStorage.setItem(this.keys.refreshToken, authResponse.refreshToken);
      localStorage.setItem(this.keys.user, JSON.stringify(authResponse.admin));
      localStorage.setItem(this.keys.expiresAt, authResponse.expiresAt);
    } catch (error) {
      console.error("Failed to store tokens:", error);
    }
  }

  /**
   * Get access token
   */
  static getAccessToken(): string | null {
    if (typeof window === "undefined") return null;

    try {
      return localStorage.getItem(this.keys.accessToken);
    } catch (error) {
      console.error("Failed to get access token:", error);
      return null;
    }
  }

  /**
   * Get refresh token
   */
  static getRefreshToken(): string | null {
    if (typeof window === "undefined") return null;

    try {
      return localStorage.getItem(this.keys.refreshToken);
    } catch (error) {
      console.error("Failed to get refresh token:", error);
      return null;
    }
  }

  /**
   * Get stored user data
   */
  static getUser(): Admin | null {
    if (typeof window === "undefined") return null;

    try {
      const userData = localStorage.getItem(this.keys.user);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Failed to get user data:", error);
      return null;
    }
  }

  /**
   * Get token expiration time
   */
  static getExpiresAt(): string | null {
    if (typeof window === "undefined") return null;

    try {
      return localStorage.getItem(this.keys.expiresAt);
    } catch (error) {
      console.error("Failed to get expiration time:", error);
      return null;
    }
  }

  /**
   * Check if tokens are expired
   */
  static isExpired(): boolean {
    const expiresAt = this.getExpiresAt();
    if (!expiresAt) return true;

    try {
      const expirationTime = new Date(expiresAt).getTime();
      const currentTime = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

      return currentTime >= expirationTime - bufferTime;
    } catch {
      return true;
    }
  }

  /**
   * Clear all stored tokens and user data
   */
  static clear(): void {
    if (typeof window === "undefined") return;

    try {
      localStorage.removeItem(this.keys.accessToken);
      localStorage.removeItem(this.keys.refreshToken);
      localStorage.removeItem(this.keys.user);
      localStorage.removeItem(this.keys.expiresAt);
    } catch (error) {
      console.error("Failed to clear tokens:", error);
    }
  }

  /**
   * Check if user is authenticated (has valid tokens)
   */
  static isAuthenticated(): boolean {
    const accessToken = this.getAccessToken();
    const user = this.getUser();

    return !!(accessToken && user && !this.isExpired());
  }

  /**
   * Get all authentication data
   */
  static getAuthData(): {
    accessToken: string | null;
    refreshToken: string | null;
    user: Admin | null;
    expiresAt: string | null;
    isAuthenticated: boolean;
  } {
    return {
      accessToken: this.getAccessToken(),
      refreshToken: this.getRefreshToken(),
      user: this.getUser(),
      expiresAt: this.getExpiresAt(),
      isAuthenticated: this.isAuthenticated(),
    };
  }

  /**
   * Update user data only (after profile updates)
   */
  static updateUser(user: Admin): void {
    if (typeof window === "undefined") return;

    try {
      localStorage.setItem(this.keys.user, JSON.stringify(user));
    } catch (error) {
      console.error("Failed to update user data:", error);
    }
  }
}
