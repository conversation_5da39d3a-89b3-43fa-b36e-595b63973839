/**
 * Authentication Context Hook
 *
 * This file provides authentication hooks and utilities following clean code principles
 * from the research of React, Next.js, Supabase, and Clerk authentication patterns.
 *
 * Following clean code principles:
 * - Single Responsibility: Each hook has one clear purpose
 * - Dependency Inversion: Depends on abstraction (AuthProvider)
 * - Interface Segregation: Provides only necessary auth methods
 * - Composition over Inheritance: Composable auth utilities
 */

import { useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/providers";

/**
 * Authentication status hook
 * Provides simplified authentication status checking
 * Following Clerk's pattern for conditional rendering
 */
export function useAuthStatus() {
  const { isAuthenticated, isLoading, isInitialized } = useAuth();

  return useMemo(
    () => ({
      isSignedIn: isAuthenticated,
      isSignedOut: !isAuthenticated && isInitialized,
      isLoading: isLoading || !isInitialized,
    }),
    [isAuthenticated, isLoading, isInitialized],
  );
}

/**
 * Authentication actions hook
 * Provides authentication actions with navigation
 * Following Next.js patterns for post-auth navigation
 */
export function useAuthActions() {
  const { login, logout } = useAuth();
  const router = useRouter();

  const loginWithRedirect = useCallback(
    async (
      credentials: LoginCredentials,
      redirectTo?: string,
    ): Promise<LoginResponse> => {
      const result = await login(credentials);

      if (result.success) {
        // Navigate to redirect URL or default dashboard
        const destination = redirectTo || "/";
        router.push(destination);
      }

      return result;
    },
    [login, router],
  );

  const logoutWithRedirect = useCallback(
    async (redirectTo?: string): Promise<void> => {
      await logout();

      // Navigate to login or specified page
      const destination = redirectTo || "/login";
      router.push(destination);
    },
    [logout, router],
  );

  return useMemo(
    () => ({
      login,
      logout,
      loginWithRedirect,
      logoutWithRedirect,
    }),
    [login, logout, loginWithRedirect, logoutWithRedirect],
  );
}

/**
 * User information hook
 * Provides user-specific data and utilities
 * Following Supabase's pattern for user data access
 */
export function useUser() {
  const { admin, isAuthenticated } = useAuth();

  return useMemo(
    () => ({
      user: admin,
      isLoaded: true, // Always loaded in our implementation
      isSignedIn: isAuthenticated,
    }),
    [admin, isAuthenticated],
  );
}

/**
 * Authentication error hook
 * Provides error handling utilities
 * Following clean code error handling patterns
 */
export function useAuthError() {
  const { error, clearError } = useAuth();

  const hasError = useMemo(() => Boolean(error), [error]);

  return useMemo(
    () => ({
      error,
      hasError,
      clearError,
    }),
    [error, hasError, clearError],
  );
}

/**
 * Token management hook
 * Provides token-related utilities
 * Following JWT best practices
 */
export function useTokens() {
  const { accessToken, refreshToken, refreshAuth } = useAuth();

  const hasValidToken = useMemo(() => Boolean(accessToken), [accessToken]);

  return useMemo(
    () => ({
      accessToken,
      refreshToken,
      hasValidToken,
      refreshAuth,
    }),
    [accessToken, refreshToken, hasValidToken, refreshAuth],
  );
}
