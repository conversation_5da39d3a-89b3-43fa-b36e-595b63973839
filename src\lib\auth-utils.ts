/**
 * Authentication Utilities
 * 
 * Utility functions for authentication following clean code principles
 * from the research of React, Next.js, Supabase, and Clerk patterns.
 * 
 * Following clean code principles:
 * - Pure Functions: No side effects in utility functions
 * - Single Responsibility: Each function has one clear purpose
 * - Immutability: Functions don't modify input parameters
 * - Testability: Easy to unit test
 */

// ============================================================================
// TOKEN UTILITIES
// ============================================================================

/**
 * Check if a JWT token is expired
 * Pure function for token validation
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch {
    return true; // Invalid token format
  }
}

/**
 * Extract token payload
 * Pure function for token decoding
 */
export function getTokenPayload(token: string): TokenPayload | null {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload as TokenPayload;
  } catch {
    return null;
  }
}

/**
 * Get token expiration time
 * Pure function returning expiration date
 */
export function getTokenExpiration(token: string): Date | null {
  const payload = getTokenPayload(token);
  return payload ? new Date(payload.exp * 1000) : null;
}

/**
 * Check if token expires within specified minutes
 * Pure function for proactive token refresh
 */
export function tokenExpiresWithin(token: string, minutes: number): boolean {
  const expiration = getTokenExpiration(token);
  if (!expiration) return true;
  
  const now = new Date();
  const threshold = new Date(now.getTime() + minutes * 60 * 1000);
  return expiration <= threshold;
}

// ============================================================================
// ROLE & PERMISSION UTILITIES
// ============================================================================

/**
 * Check if user has specific role
 * Pure function for role checking
 */
export function hasRole(admin: Admin | null, role: AdminRole): boolean {
  return admin?.roles?.includes(role) ?? false;
}

/**
 * Check if user has any of the specified roles
 * Pure function for multiple role checking
 */
export function hasAnyRole(admin: Admin | null, roles: AdminRole[]): boolean {
  return roles.some(role => hasRole(admin, role));
}

/**
 * Check if user has all specified roles
 * Pure function for multiple role requirement
 */
export function hasAllRoles(admin: Admin | null, roles: AdminRole[]): boolean {
  return roles.every(role => hasRole(admin, role));
}

/**
 * Check if user has specific permission
 * Pure function for permission checking
 */
export function hasPermission(admin: Admin | null, permission: string): boolean {
  if (!admin) return false;
  
  // Extract permissions from token payload if available
  const permissions = admin.permissions || [];
  return permissions.includes(permission);
}

/**
 * Get user's highest role priority
 * Pure function for role hierarchy
 */
export function getHighestRolePriority(admin: Admin | null): number {
  if (!admin?.roles) return 0;
  
  const rolePriorities: Record<AdminRole, number> = {
    'super_admin': 100,
    'admin': 80,
    'manager': 60,
    'user': 40,
  };
  
  return Math.max(...admin.roles.map(role => rolePriorities[role] || 0));
}

// ============================================================================
// AUTHENTICATION STATE UTILITIES
// ============================================================================

/**
 * Create authentication state
 * Pure function for state creation
 */
export function createAuthState(
  admin: Admin | null,
  accessToken: string | null,
  refreshToken: string | null
): AuthState {
  return {
    isAuthenticated: Boolean(admin && accessToken),
    admin,
    accessToken,
    refreshToken,
    expiresAt: accessToken ? getTokenExpiration(accessToken)?.toISOString() || null : null,
    isLoading: false,
    error: null,
  };
}

/**
 * Check if authentication state is valid
 * Pure function for state validation
 */
export function isAuthStateValid(authState: AuthState): boolean {
  const { isAuthenticated, admin, accessToken } = authState;
  
  if (!isAuthenticated) return true; // Valid unauthenticated state
  
  return Boolean(
    admin &&
    accessToken &&
    !isTokenExpired(accessToken)
  );
}

// ============================================================================
// URL & REDIRECT UTILITIES
// ============================================================================

/**
 * Create login URL with redirect parameter
 * Pure function for URL construction
 */
export function createLoginUrl(redirectTo?: string, baseLoginPath = "/login"): string {
  if (!redirectTo) return baseLoginPath;
  
  const url = new URL(baseLoginPath, window.location.origin);
  url.searchParams.set("redirect", redirectTo);
  return url.pathname + url.search;
}

/**
 * Extract redirect URL from search params
 * Pure function for redirect extraction
 */
export function getRedirectUrl(searchParams: URLSearchParams, defaultRedirect = "/"): string {
  const redirect = searchParams.get("redirect");
  
  // Validate redirect URL to prevent open redirects
  if (redirect && isValidRedirectUrl(redirect)) {
    return redirect;
  }
  
  return defaultRedirect;
}

/**
 * Validate redirect URL for security
 * Pure function for security validation
 */
export function isValidRedirectUrl(url: string): boolean {
  try {
    // Only allow relative URLs or same-origin URLs
    if (url.startsWith("/")) return true;
    
    const redirectUrl = new URL(url);
    const currentOrigin = window.location.origin;
    
    return redirectUrl.origin === currentOrigin;
  } catch {
    return false;
  }
}

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Create authentication error
 * Pure function for error creation
 */
export function createAuthError(
  message: string,
  code: string,
  type: AuthErrorType = AuthErrorType.UNKNOWN_ERROR
): AuthError {
  return {
    message,
    code,
    type,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Check if error is authentication-related
 * Pure function for error classification
 */
export function isAuthError(error: unknown): error is AuthError {
  return (
    typeof error === "object" &&
    error !== null &&
    "type" in error &&
    Object.values(AuthErrorType).includes((error as any).type)
  );
}

/**
 * Get user-friendly error message
 * Pure function for error message formatting
 */
export function getAuthErrorMessage(error: AuthError): string {
  const errorMessages: Record<AuthErrorType, string> = {
    [AuthErrorType.INVALID_CREDENTIALS]: "Invalid email or password",
    [AuthErrorType.TOKEN_EXPIRED]: "Your session has expired. Please log in again",
    [AuthErrorType.TOKEN_INVALID]: "Invalid authentication token",
    [AuthErrorType.NETWORK_ERROR]: "Network error. Please check your connection",
    [AuthErrorType.SERVER_ERROR]: "Server error. Please try again later",
    [AuthErrorType.UNKNOWN_ERROR]: "An unexpected error occurred",
  };
  
  return errorMessages[error.type] || error.message;
}

// ============================================================================
// STORAGE UTILITIES
// ============================================================================

/**
 * Check if localStorage is available
 * Pure function for storage availability
 */
export function isLocalStorageAvailable(): boolean {
  try {
    const test = "__localStorage_test__";
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * Safely get item from localStorage
 * Pure function with error handling
 */
export function getStorageItem(key: string): string | null {
  if (!isLocalStorageAvailable()) return null;
  
  try {
    return localStorage.getItem(key);
  } catch {
    return null;
  }
}

/**
 * Safely set item in localStorage
 * Function with error handling
 */
export function setStorageItem(key: string, value: string): boolean {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    localStorage.setItem(key, value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Safely remove item from localStorage
 * Function with error handling
 */
export function removeStorageItem(key: string): boolean {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    localStorage.removeItem(key);
    return true;
  } catch {
    return false;
  }
}
