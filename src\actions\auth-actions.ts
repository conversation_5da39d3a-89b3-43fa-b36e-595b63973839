"use server";

import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import { authService } from "@/services/auth";
import { FormLoginData } from "@/schemas";

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax" as const,
  maxAge: 60 * 60 * 24 * 7, // 7 days
  path: "/",
};

/**
 * Server action for user login
 * Supports both old (username) and new (email) formats during Phase 1 transition
 */
export async function login(
  credentials: FormLoginData,
): Promise<LoginResponse> {
  try {
    // Call the auth service directly - it handles credential normalization
    const result = await authService.login(credentials);

    if (result.success && result.data) {
      // Set secure HTTP-only cookies for authentication
      const cookieStore = await cookies();

      cookieStore.set("auth_token", result.data.accessToken, COOKIE_OPTIONS);
      cookieStore.set(
        "auth_user",
        JSON.stringify(result.data.admin),
        COOKIE_OPTIONS,
      );

      // Set refresh token with longer expiry
      cookieStore.set("refresh_token", result.data.refreshToken, {
        ...COOKIE_OPTIONS,
        maxAge: 60 * 60 * 24 * 30, // 30 days
      });

      return result;
    }

    return result;
  } catch (error) {
    console.error("Login action error:", error);
    return {
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "An unexpected error occurred. Please try again.",
        details:
          error instanceof Error
            ? { message: error.message, stack: error.stack }
            : { error: String(error) },
      },
    };
  }
}

/**
 * Server action for user logout
 */
export async function logout(): Promise<LogoutResponse> {
  try {
    // Call the auth service
    const result = await authService.logout();

    // Clear authentication cookies
    const cookieStore = await cookies();
    cookieStore.delete("auth_token");
    cookieStore.delete("auth_user");
    cookieStore.delete("refresh_token");

    return result;
  } catch (error) {
    console.error("Logout action error:", error);
    return {
      success: false,
      message: "An error occurred during logout",
    };
  }
}

/**
 * Server action to check if user is authenticated
 */
export async function getIsAuthenticated(): Promise<boolean> {
  try {
    const user = await getCurrentUser();
    return user !== null;
  } catch (error) {
    console.error("Is authenticated action error:", error);
    return false;
  }
}

/**
 * Helper function to construct login URL with redirect parameter
 */
function constructLoginUrl(redirectTo: string): string {
  // Parse the login path to handle existing query parameters
  const [loginPath, existingQuery] = "/login".split("?");
  const params = new URLSearchParams(existingQuery || "");
  params.set("redirect", redirectTo);
  return `${loginPath}?${params.toString()}`;
}

/**
 * Server action to redirect to login if not authenticated
 */
export async function requireAuth(redirectTo?: string) {
  const isAuthenticated = await getIsAuthenticated();

  if (!isAuthenticated) {
    const loginUrl = redirectTo ? constructLoginUrl(redirectTo) : "/login";
    redirect(loginUrl);
  }
}

/**
 * Server action to redirect authenticated users away from auth pages
 */
export async function redirectIfAuthenticated(redirectTo: string = "/") {
  const isAuthenticated = await getIsAuthenticated();

  if (isAuthenticated) {
    redirect(redirectTo);
  }
}

/**
 * Server action for form-based login with redirect
 * Updated to support email field (Phase 1 transition)
 */
export async function loginForm(formData: FormData) {
  // Support both email and username fields during transition
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const redirectTo = formData.get("redirect") as string;

  if (!email || !password) {
    return {
      success: false,
      error: {
        code: "MISSING_CREDENTIALS",
        message: "Email/username and password are required",
      },
    };
  }

  // Use email format if available, otherwise username (for backward compatibility)
  const credentials = {
    email, // Use email
    password,
  };

  const result = await login(credentials);

  if (result.success) {
    // Redirect to dashboard or specified redirect URL
    const redirectUrl = redirectTo && redirectTo !== "null" ? redirectTo : "/";
    redirect(redirectUrl);
  }

  return result;
}

/**
 * Server action for logout with redirect
 */
export async function startLogout() {
  await logout();
  redirect("/login");
}

/**
 * Server action to validate authentication token
 * Used by client components to validate tokens without importing server-side services
 */
export async function validateToken(
  token: string,
): Promise<ValidateTokenResponse> {
  try {
    const result = await authService.validateToken(token);
    return result;
  } catch (error) {
    console.error("Token validation action error:", error);
    return {
      valid: false,
      error: {
        code: "TOKEN_VALIDATION_ERROR",
        message:
          error instanceof Error ? error.message : "Token validation failed",
      },
    };
  }
}

/**
 * Server action to get current user profile
 * Used by client components to get user data without importing server-side services
 */
export async function getCurrentUser(): Promise<ValidateTokenResponse> {
  try {
    const cookieStore = await cookies();
    const authToken = cookieStore.get("auth_token")?.value;

    if (!authToken) {
      return {
        valid: false,
        error: {
          code: "NO_TOKEN",
          message: "No authentication token found",
        },
      };
    }

    return await authService.validateToken(authToken);
  } catch (error) {
    console.error("Get current user action error:", error);
    return {
      valid: false,
      error: {
        code: "USER_FETCH_ERROR",
        message:
          error instanceof Error ? error.message : "Failed to get user data",
      },
    };
  }
}
